import { Controller, Get, Query, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Inject } from '@midwayjs/core';

import { ServiceDurationRecord } from '../../entity/service-duration-record.entity';
import { Order } from '../../entity/order.entity';
import { OrderDetail } from '../../entity/order-detail.entity';
import { Service } from '../../entity/service.entity';
import { AdditionalService } from '../../entity/additional-service.entity';
import { Employee } from '../../entity/employee.entity';
import { Customer } from '../../entity/customer.entity';
import { Op } from 'sequelize';

@Controller('/admin/service-duration-statistics')
export class AdminServiceDurationStatisticsController {
  @Inject()
  ctx: Context;

  @Get('/records', { summary: '查询服务时长记录' })
  async getServiceDurationRecords(
    @Query('orderId') orderId?: number,
    @Query('employeeId') employeeId?: number,
    @Query('serviceId') serviceId?: number,
    @Query('additionalServiceId') additionalServiceId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20
  ) {
    const where: any = {};

    if (orderId) {
      where.orderId = orderId;
    }

    if (employeeId) {
      where.employeeId = employeeId;
    }

    if (serviceId) {
      where.serviceId = serviceId;
    }

    if (additionalServiceId) {
      where.additionalServiceId = additionalServiceId;
    }

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.startTime[Op.lte] = new Date(endDate);
      }
    }

    const offset = (page - 1) * pageSize;

    const { rows: records, count } =
      await ServiceDurationRecord.findAndCountAll({
        where,
        include: [
          {
            model: Order,
            attributes: ['id', 'sn', 'status'],
            include: [
              {
                model: Customer,
                attributes: ['id', 'nickname', 'phone'],
              },
            ],
          },
          {
            model: OrderDetail,
            attributes: ['id', 'serviceName', 'petName'],
          },
          {
            model: Employee,
            attributes: ['id', 'name', 'phone'],
          },
          {
            model: Service,
            attributes: ['id', 'serviceName', 'avgDuration'],
          },
          {
            model: AdditionalService,
            attributes: ['id', 'name', 'duration'],
          },
        ],
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset,
      });

    return {
      records,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize),
      },
    };
  }

  @Get('/order/:orderId', { summary: '按订单查询时长统计详情' })
  async getOrderDurationStatistics(@Param('orderId') orderId: number) {
    // 查询订单的所有服务时长记录
    const records = await ServiceDurationRecord.findAll({
      where: {
        orderId,
        duration: { [Op.not]: null }, // 只查询已完成的记录
      },
      include: [
        {
          model: Service,
          attributes: ['id', 'serviceName', 'avgDuration'],
        },
        {
          model: AdditionalService,
          attributes: ['id', 'name', 'duration'],
        },
        {
          model: Employee,
          attributes: ['id', 'name'],
        },
      ],
      order: [['startTime', 'ASC']],
    });

    // 按类型分组统计
    const mainServiceRecords = records.filter(
      r => r.recordType === 'main_service'
    );
    const additionalServiceRecords = records.filter(
      r => r.recordType === 'additional_service'
    );

    const mainServiceDuration = mainServiceRecords.reduce(
      (sum, r) => sum + (r.duration || 0),
      0
    );
    const additionalServiceDuration = additionalServiceRecords.reduce(
      (sum, r) => sum + (r.duration || 0),
      0
    );

    const totalDuration = mainServiceDuration + additionalServiceDuration;

    // 计算服务跨度时长
    let serviceDuration = 0;
    if (records.length > 0) {
      const startTime = records[0].startTime;
      const endTime = records[records.length - 1].endTime;
      if (startTime && endTime) {
        serviceDuration = Math.round(
          (endTime.getTime() - startTime.getTime()) / (1000 * 60)
        );
      }
    }

    return {
      orderId,
      totalRecords: records.length,
      mainServiceRecords: mainServiceRecords.length,
      additionalServiceRecords: additionalServiceRecords.length,
      totalDuration, // 累计时长
      mainServiceDuration,
      additionalServiceDuration,
      serviceDuration, // 服务跨度时长
      records: records.map(record => ({
        id: record.id,
        recordType: record.recordType,
        serviceName: record.serviceName || record.additionalServiceName,
        startTime: record.startTime,
        endTime: record.endTime,
        duration: record.duration,
        employee: record.employee,
        service: record.service,
        additionalService: record.additionalService,
      })),
    };
  }

  @Get('/service/:serviceId', { summary: '按主服务查询时长记录' })
  async getServiceDurationRecordsByService(
    @Param('serviceId') serviceId: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20
  ) {
    const where: any = {
      recordType: 'main_service',
      serviceId,
      duration: { [Op.not]: null }, // 只查询已完成的记录
    };

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.startTime[Op.lte] = new Date(endDate);
      }
    }

    const offset = (page - 1) * pageSize;

    const { rows: records, count } =
      await ServiceDurationRecord.findAndCountAll({
        where,
        include: [
          {
            model: Order,
            attributes: ['id', 'sn', 'status'],
            include: [
              {
                model: Customer,
                attributes: ['id', 'nickname', 'phone'],
              },
            ],
          },
          {
            model: OrderDetail,
            attributes: ['id', 'serviceName', 'petName'],
          },
          {
            model: Employee,
            attributes: ['id', 'name', 'phone'],
          },
          {
            model: Service,
            attributes: ['id', 'serviceName', 'avgDuration'],
          },
        ],
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset,
      });

    // 计算统计信息
    const totalDuration = records.reduce(
      (sum, record) => sum + (record.duration || 0),
      0
    );
    const avgDuration =
      records.length > 0 ? Math.round(totalDuration / records.length) : 0;
    const minDuration =
      records.length > 0 ? Math.min(...records.map(r => r.duration || 0)) : 0;
    const maxDuration =
      records.length > 0 ? Math.max(...records.map(r => r.duration || 0)) : 0;

    return {
      serviceId,
      serviceName: records[0]?.service?.serviceName || '',
      systemAvgDuration: records[0]?.service?.avgDuration || 0,
      statistics: {
        totalRecords: count,
        totalDuration,
        avgDuration,
        minDuration,
        maxDuration,
      },
      records,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize),
      },
    };
  }

  @Get('/additional-service/:additionalServiceId', {
    summary: '按增项服务查询时长记录',
  })
  async getAdditionalServiceDurationRecords(
    @Param('additionalServiceId') additionalServiceId: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20
  ) {
    const where: any = {
      recordType: 'additional_service',
      additionalServiceId,
      duration: { [Op.not]: null }, // 只查询已完成的记录
    };

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.startTime[Op.lte] = new Date(endDate);
      }
    }

    const offset = (page - 1) * pageSize;

    const { rows: records, count } =
      await ServiceDurationRecord.findAndCountAll({
        where,
        include: [
          {
            model: Order,
            attributes: ['id', 'sn', 'status'],
            include: [
              {
                model: Customer,
                attributes: ['id', 'nickname', 'phone'],
              },
            ],
          },
          {
            model: OrderDetail,
            attributes: ['id', 'serviceName', 'petName'],
          },
          {
            model: Employee,
            attributes: ['id', 'name', 'phone'],
          },
          {
            model: AdditionalService,
            attributes: ['id', 'name', 'duration'],
          },
        ],
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset,
      });

    // 计算统计信息
    const totalDuration = records.reduce(
      (sum, record) => sum + (record.duration || 0),
      0
    );
    const avgDuration =
      records.length > 0 ? Math.round(totalDuration / records.length) : 0;
    const minDuration =
      records.length > 0 ? Math.min(...records.map(r => r.duration || 0)) : 0;
    const maxDuration =
      records.length > 0 ? Math.max(...records.map(r => r.duration || 0)) : 0;

    return {
      additionalServiceId,
      additionalServiceName: records[0]?.additionalService?.name || '',
      systemAvgDuration: records[0]?.additionalService?.duration || 0,
      statistics: {
        totalRecords: count,
        totalDuration,
        avgDuration,
        minDuration,
        maxDuration,
      },
      records,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize),
      },
    };
  }
}
