import { Controller, Get, Query, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Inject } from '@midwayjs/core';

import { ServiceDurationRecord } from '../../entity/service-duration-record.entity';
import { Order } from '../../entity/order.entity';
import { OrderDetail } from '../../entity/order-detail.entity';
import { Service } from '../../entity/service.entity';
import { AdditionalService } from '../../entity/additional-service.entity';
import { Employee } from '../../entity/employee.entity';
import { Customer } from '../../entity/customer.entity';
import { Op } from 'sequelize';
import { OrderDurationCalculatorService } from '../../service/order-duration-calculator.service';

@Controller('/admin/service-duration-statistics')
export class AdminServiceDurationStatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  orderDurationCalculatorService: OrderDurationCalculatorService;

  @Get('/records', { summary: '查询服务时长记录' })
  async getServiceDurationRecords(
    @Query('orderId') orderId?: number,
    @Query('employeeId') employeeId?: number,
    @Query('serviceId') serviceId?: number,
    @Query('additionalServiceId') additionalServiceId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20
  ) {
    const where: any = {};

    if (orderId) {
      where.orderId = orderId;
    }

    if (employeeId) {
      where.employeeId = employeeId;
    }

    if (serviceId) {
      where.serviceId = serviceId;
    }

    if (additionalServiceId) {
      where.additionalServiceId = additionalServiceId;
    }

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        where.startTime[Op.lte] = new Date(endDate);
      }
    }

    const offset = (page - 1) * pageSize;

    const { rows: records, count } =
      await ServiceDurationRecord.findAndCountAll({
        where,
        include: [
          {
            model: Order,
            attributes: ['id', 'sn', 'status'],
            include: [
              {
                model: Customer,
                attributes: ['id', 'nickname', 'phone'],
              },
            ],
          },
          {
            model: OrderDetail,
            attributes: ['id', 'serviceName', 'petName'],
          },
          {
            model: Employee,
            attributes: ['id', 'name', 'phone'],
          },
          {
            model: Service,
            attributes: ['id', 'serviceName', 'avgDuration'],
          },
          {
            model: AdditionalService,
            attributes: ['id', 'name', 'duration'],
          },
        ],
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset,
      });

    return {
      records,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize),
      },
    };
  }





  @Get('/order-duration/:orderId', { summary: '获取订单时长统计详情' })
  async getOrderDurationStatistics(@Param('orderId') orderId: number) {
    return await this.orderDurationCalculatorService.getOrderDurationStatistics(
      orderId
    );
  }
}
