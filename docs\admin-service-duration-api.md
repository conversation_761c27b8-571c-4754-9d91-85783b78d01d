# 管理端服务时长接口文档

## 概述
本文档描述了管理端服务时长相关的API接口，主要用于查询和管理服务时长记录。

## 接口列表

### 1. 查询服务时长记录

**接口地址：** `GET /admin/service-duration-statistics/records`

**接口描述：** 管理端查询服务时长记录，支持多维度筛选和分页

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 否 | 订单ID筛选 |
| employeeId | number | 否 | 员工ID筛选 |
| serviceId | number | 否 | 主服务ID筛选 |
| additionalServiceId | number | 否 | 增项服务ID筛选 |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "orderId": 123,
        "orderDetailId": 456,
        "additionalServiceOrderId": null,
        "employeeId": 1,
        "recordType": "main_service",
        "serviceId": 789,
        "serviceName": "基础洗护",
        "additionalServiceId": null,
        "additionalServiceName": null,
        "startTime": "2024-01-01T10:00:00.000Z",
        "endTime": "2024-01-01T10:45:00.000Z",
        "duration": 45,
        "remark": "服务完成",
        "createdAt": "2024-01-01T10:00:00.000Z",
        "updatedAt": "2024-01-01T10:45:00.000Z",
        "order": {
          "id": 123,
          "sn": "20240101001",
          "status": "已完成",
          "customer": {
            "id": 1,
            "nickname": "小明",
            "phone": "13800138001"
          }
        },
        "orderDetail": {
          "id": 456,
          "serviceName": "基础洗护",
          "petName": "小白"
        },
        "employee": {
          "id": 1,
          "name": "李师傅",
          "phone": "13900139000"
        },
        "service": {
          "id": 789,
          "serviceName": "基础洗护",
          "avgDuration": 45
        },
        "additionalService": null
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "pageSize": 20,
      "totalPages": 5
    }
  }
}
```

### 2. 按订单查询时长统计详情

**接口地址：** `GET /admin/service-duration-statistics/order/{orderId}`

**接口描述：** 管理端查询指定订单的时长统计详情，包含汇总信息和详细记录

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "totalRecords": 3,
    "mainServiceRecords": 2,
    "additionalServiceRecords": 1,
    "totalDuration": 120,
    "mainServiceDuration": 90,
    "additionalServiceDuration": 30,
    "serviceDuration": 135,
    "records": [
      {
        "id": 1,
        "recordType": "main_service",
        "serviceName": "基础洗护",
        "startTime": "2024-01-01T10:00:00.000Z",
        "endTime": "2024-01-01T10:45:00.000Z",
        "duration": 45,
        "employee": {
          "id": 1,
          "name": "李师傅"
        },
        "service": {
          "id": 789,
          "serviceName": "基础洗护",
          "avgDuration": 45
        }
      }
    ]
  }
}
```

### 3. 按主服务查询时长记录

**接口地址：** `GET /admin/service-duration-statistics/service/{serviceId}`

**接口描述：** 管理端查询指定主服务的所有时长记录和统计信息

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| serviceId | number | 是 | 主服务ID |

**查询参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "serviceId": 789,
    "serviceName": "基础洗护",
    "systemAvgDuration": 45,
    "statistics": {
      "totalRecords": 50,
      "totalDuration": 2250,
      "avgDuration": 45,
      "minDuration": 30,
      "maxDuration": 60
    },
    "records": [
      {
        "id": 1,
        "orderId": 123,
        "duration": 45,
        "startTime": "2024-01-01T10:00:00.000Z",
        "endTime": "2024-01-01T10:45:00.000Z",
        "order": {
          "id": 123,
          "sn": "20240101001",
          "status": "已完成",
          "customer": {
            "id": 1,
            "nickname": "小明",
            "phone": "13800138001"
          }
        },
        "employee": {
          "id": 1,
          "name": "李师傅",
          "phone": "13900139000"
        }
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "pageSize": 20,
      "totalPages": 3
    }
  }
}
```

### 4. 按增项服务查询时长记录

**接口地址：** `GET /admin/service-duration-statistics/additional-service/{additionalServiceId}`

**接口描述：** 管理端查询指定增项服务的所有时长记录和统计信息

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| additionalServiceId | number | 是 | 增项服务ID |

**查询参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "additionalServiceId": 456,
    "additionalServiceName": "全身去油",
    "systemAvgDuration": 30,
    "statistics": {
      "totalRecords": 25,
      "totalDuration": 750,
      "avgDuration": 30,
      "minDuration": 20,
      "maxDuration": 40
    },
    "records": [
      {
        "id": 2,
        "orderId": 123,
        "duration": 30,
        "startTime": "2024-01-01T11:00:00.000Z",
        "endTime": "2024-01-01T11:30:00.000Z",
        "order": {
          "id": 123,
          "sn": "20240101001",
          "status": "已完成",
          "customer": {
            "id": 1,
            "nickname": "小明",
            "phone": "13800138001"
          }
        },
        "employee": {
          "id": 1,
          "name": "李师傅",
          "phone": "13900139000"
        }
      }
    ],
    "pagination": {
      "total": 25,
      "page": 1,
      "pageSize": 20,
      "totalPages": 2
    }
  }
}
```

## 用户端和员工端接口

### 用户端查询订单时长记录
**接口地址：** `GET /user/service-duration/records/{orderId}`
**接口描述：** 用户查询自己订单的服务时长记录
**权限验证：** 验证订单归属

### 用户端查询订单时长统计详情
**接口地址：** `GET /user/service-duration/duration-statistics/{orderId}`
**接口描述：** 用户查询自己订单的时长统计详情
**权限验证：** 验证订单归属

### 员工端查询订单时长记录
**接口地址：** `GET /employee/service-duration/records/{orderId}`
**接口描述：** 员工查询订单的服务时长记录

## 数据字段说明

### 记录类型 (recordType)
- `main_service`: 主服务
- `additional_service`: 增项服务

### 时间字段
- `startTime`: 服务开始时间，ISO 8601格式
- `endTime`: 服务结束时间，ISO 8601格式
- `duration`: 服务时长，单位为分钟

### 关联字段
- `serviceId`: 主服务ID（主服务记录时使用）
- `additionalServiceId`: 增项服务ID（增项服务记录时使用）
- `orderDetailId`: 订单详情ID（主服务和主订单增项服务时使用）
- `additionalServiceOrderId`: 追加服务订单ID（追加服务增项时使用）

## 平均时长自动更新机制

系统会在每次服务结束时自动更新平均时长：

1. **主服务平均时长**：基于最近10次服务记录计算，更新到 `services.avgDuration` 字段
2. **增项服务平均时长**：基于最近10次服务记录计算，更新到 `additional_services.duration` 字段

### 更新触发时机
- 员工结束单个服务计时时
- 员工手动完成订单时（自动结束所有未完成的服务）

### 计算规则
- 只统计已完成的服务记录（有 `endTime` 和 `duration` 的记录）
- 按创建时间倒序取最近10次记录
- 计算平均值并四舍五入到分钟

## 注意事项

1. **权限控制**：管理端接口需要管理员权限，用户端接口需要验证订单归属
2. **数据完整性**：只有已完成的服务才会有完整的时长数据
3. **时区处理**：所有时间均为服务器时间（UTC+8）
4. **分页查询**：默认按创建时间倒序排列
5. **自动更新**：平均时长会在服务结束时自动更新，无需手动干预
