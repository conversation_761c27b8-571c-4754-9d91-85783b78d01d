# 管理端服务时长查询接口文档

## 概述
本文档提供管理端服务时长查询的四个核心接口，用于前台页面开发。

**基础路径：** `/admin/service-duration-statistics`

**认证要求：** 需要管理员权限

**返回格式：** 统一返回格式 `{ errCode, msg, data }`

---

## 1. 按员工查询时长记录

**接口地址：** `GET /admin/service-duration-statistics/records`

**接口描述：** 查询指定员工的服务时长记录

**查询参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**请求示例：**
```
GET /admin/service-duration-statistics/records?employeeId=1&startDate=2024-01-01&endDate=2024-01-31&page=1&pageSize=20
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "orderId": 123,
        "recordType": "main_service",
        "serviceName": "基础洗护",
        "startTime": "2024-01-01T10:00:00.000Z",
        "endTime": "2024-01-01T10:45:00.000Z",
        "duration": 45,
        "order": {
          "id": 123,
          "sn": "20240101001",
          "status": "已完成",
          "customer": {
            "id": 1,
            "nickname": "小明",
            "phone": "13800138001"
          }
        },
        "employee": {
          "id": 1,
          "name": "李师傅",
          "phone": "13900139000"
        }
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "pageSize": 20,
      "totalPages": 3
    }
  }
}
```

---

## 2. 按订单查询时长统计详情

**接口地址：** `GET /admin/service-duration-statistics/order/{orderId}`

**接口描述：** 查询指定订单的完整时长统计详情

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求示例：**
```
GET /admin/service-duration-statistics/order/123
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "totalRecords": 3,
    "mainServiceRecords": 2,
    "additionalServiceRecords": 1,
    "totalDuration": 120,
    "mainServiceDuration": 90,
    "additionalServiceDuration": 30,
    "serviceDuration": 135,
    "records": [
      {
        "id": 1,
        "recordType": "main_service",
        "serviceName": "基础洗护",
        "startTime": "2024-01-01T10:00:00.000Z",
        "endTime": "2024-01-01T10:45:00.000Z",
        "duration": 45,
        "employee": {
          "id": 1,
          "name": "李师傅"
        },
        "service": {
          "id": 789,
          "serviceName": "基础洗护",
          "avgDuration": 45
        }
      }
    ]
  }
}
```

**字段说明：**
- `totalDuration`: 累计时长（所有服务时长之和）
- `serviceDuration`: 服务跨度时长（从第一个服务开始到最后一个服务结束的总时间）

---

## 3. 按主服务查询时长记录

**接口地址：** `GET /admin/service-duration-statistics/service/{serviceId}`

**接口描述：** 查询指定主服务的所有时长记录和统计信息

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| serviceId | number | 是 | 主服务ID |

**查询参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**请求示例：**
```
GET /admin/service-duration-statistics/service/789?startDate=2024-01-01&endDate=2024-01-31&page=1&pageSize=20
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "serviceId": 789,
    "serviceName": "基础洗护",
    "systemAvgDuration": 45,
    "statistics": {
      "totalRecords": 50,
      "totalDuration": 2250,
      "avgDuration": 45,
      "minDuration": 30,
      "maxDuration": 60
    },
    "records": [
      {
        "id": 1,
        "orderId": 123,
        "duration": 45,
        "startTime": "2024-01-01T10:00:00.000Z",
        "endTime": "2024-01-01T10:45:00.000Z",
        "order": {
          "id": 123,
          "sn": "20240101001",
          "status": "已完成",
          "customer": {
            "id": 1,
            "nickname": "小明",
            "phone": "13800138001"
          }
        },
        "employee": {
          "id": 1,
          "name": "李师傅",
          "phone": "13900139000"
        }
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "pageSize": 20,
      "totalPages": 3
    }
  }
}
```

**字段说明：**
- `systemAvgDuration`: 系统记录的平均时长（来自服务表）
- `statistics.avgDuration`: 实际计算的平均时长（基于查询结果）

---

## 4. 按增项服务查询时长记录

**接口地址：** `GET /admin/service-duration-statistics/additional-service/{additionalServiceId}`

**接口描述：** 查询指定增项服务的所有时长记录和统计信息

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| additionalServiceId | number | 是 | 增项服务ID |

**查询参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**请求示例：**
```
GET /admin/service-duration-statistics/additional-service/456?startDate=2024-01-01&endDate=2024-01-31&page=1&pageSize=20
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "additionalServiceId": 456,
    "additionalServiceName": "全身去油",
    "systemAvgDuration": 30,
    "statistics": {
      "totalRecords": 25,
      "totalDuration": 750,
      "avgDuration": 30,
      "minDuration": 20,
      "maxDuration": 40
    },
    "records": [
      {
        "id": 2,
        "orderId": 123,
        "duration": 30,
        "startTime": "2024-01-01T11:00:00.000Z",
        "endTime": "2024-01-01T11:30:00.000Z",
        "order": {
          "id": 123,
          "sn": "20240101001",
          "status": "已完成",
          "customer": {
            "id": 1,
            "nickname": "小明",
            "phone": "13800138001"
          }
        },
        "employee": {
          "id": 1,
          "name": "李师傅",
          "phone": "13900139000"
        }
      }
    ],
    "pagination": {
      "total": 25,
      "page": 1,
      "pageSize": 20,
      "totalPages": 2
    }
  }
}
```

**字段说明：**
- `systemAvgDuration`: 系统记录的平均时长（来自增项服务表）
- `statistics.avgDuration`: 实际计算的平均时长（基于查询结果）

---

## 通用字段说明

### 记录类型 (recordType)
- `main_service`: 主服务
- `additional_service`: 增项服务

### 时间字段
- `startTime`: 服务开始时间，ISO 8601格式
- `endTime`: 服务结束时间，ISO 8601格式
- `duration`: 服务时长，单位为分钟

### 分页字段
- `total`: 总记录数
- `page`: 当前页码
- `pageSize`: 每页数量
- `totalPages`: 总页数

### 统计字段
- `totalRecords`: 总记录数
- `totalDuration`: 总时长（分钟）
- `avgDuration`: 平均时长（分钟）
- `minDuration`: 最短时长（分钟）
- `maxDuration`: 最长时长（分钟）

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 使用建议

1. **分页查询**：建议使用合适的页面大小（10-50），避免一次查询过多数据
2. **时间筛选**：使用日期范围筛选可以提高查询性能
3. **缓存策略**：统计数据可以考虑适当缓存，减少重复查询
4. **权限验证**：所有接口都需要管理员权限，前端需要处理权限验证失败的情况
